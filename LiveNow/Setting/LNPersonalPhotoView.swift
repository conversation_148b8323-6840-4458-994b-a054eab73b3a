//
//  LNPersonalPhotoView.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit

/// Personal Photo 横向滑动视图
/// 展示用户的个人照片集合，支持横向滑动浏览
class LNPersonalPhotoView: UIView {
    
    // MARK: - Properties
    private var photos: [LNAnchorFileModel] = []
    
    // MARK: - UI Elements
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "📷 Personal photo"
        label.font = LNFont.bold(16)
        label.textColor = UIColor.hex(hexString: "#04E798")
        return label
    }()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: s(15), bottom: 0, right: s(15))
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNPersonalPhotoCell.self, forCellWithReuseIdentifier: "LNPersonalPhotoCell")
        return collectionView
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        
        addSubview(titleLabel)
        addSubview(collectionView)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(s(15))
            make.right.equalToSuperview().offset(-s(15))
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(120))
            make.bottom.equalToSuperview().offset(-s(15))
        }
    }

    // MARK: - Public Methods

    /// 更新照片数据
    func updatePhotos(_ newPhotos: [LNAnchorFileModel]) {
        self.photos = newPhotos

        DispatchQueue.main.async {
            self.collectionView.reloadData()

            // 根据数据状态调整视图显示
            if newPhotos.isEmpty {
                // 没有照片时，隐藏整个视图或设置最小高度
                self.collectionView.snp.updateConstraints { make in
                    make.height.equalTo(0)
                }
            } else {
                // 有照片时，恢复正常高度
                self.collectionView.snp.updateConstraints { make in
                    make.height.equalTo(s(120))
                }
            }
        }
    }
}

// MARK: - UICollectionViewDataSource
extension LNPersonalPhotoView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return photos.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNPersonalPhotoCell", for: indexPath) as! LNPersonalPhotoCell
        let photoModel = photos[indexPath.item]
        cell.configure(with: photoModel)
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNPersonalPhotoView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let height = s(120)
        let width = s(90)
        return CGSize(width: width, height: height)
    }
}

// MARK: - UICollectionViewDelegate
extension LNPersonalPhotoView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let photo = photos[indexPath.item]
        print("Photo selected: \(photo)")
        // TODO: 实现照片预览功能
    }
}

// MARK: - LNPersonalPhotoCell
class LNPersonalPhotoCell: UICollectionViewCell {
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = s(8)
        imageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func configure(with photoModel: LNAnchorFileModel) {
        // 使用LNImageLoader加载图片
        if !photoModel.displayUrl.isEmpty {
            LNImageLoader.loadImage(
                imageView,
                url: photoModel.displayUrl,
                placeholder: UIImage(named: "placeholder_image")
            )
        } else {
            // 如果没有URL，尝试使用fileName作为本地图片名
            imageView.image = UIImage(named: photoModel.fileName) ?? UIImage(named: "placeholder_image")
        }
    }
}
