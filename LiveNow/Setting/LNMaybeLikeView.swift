//
//  LNMaybeLikeView.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit

/// Maybe Like 横向滑动视图
/// 展示可能喜欢的用户列表，支持横向滑动浏览
class LNMaybeLikeView: UIView {
    
    // MARK: - Properties
    private var users: [LNUserModel] = []
    private let defaultUsers = [
        UserItem(name: "NAME", country: "Indonesia", avatar: "default_avatar"),
        UserItem(name: "NAME", country: "Indonesia", avatar: "default_avatar"),
        UserItem(name: "NAME", country: "Indonesia", avatar: "default_avatar"),
        UserItem(name: "NAME", country: "Indonesia", avatar: "default_avatar"),
        UserItem(name: "NAME", country: "Indonesia", avatar: "default_avatar")
    ]
    
    // MARK: - UI Elements
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "💖 Maybe like"
        label.font = LNFont.bold(16)
        label.textColor = UIColor.hex(hexString: "#04E798")
        return label
    }()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: s(15), bottom: 0, right: s(15))
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNMaybeLikeUserCell.self, forCellWithReuseIdentifier: "LNMaybeLikeUserCell")
        return collectionView
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupDefaultUsers()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        
        addSubview(titleLabel)
        addSubview(collectionView)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(s(15))
            make.right.equalToSuperview().offset(-s(15))
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(160))
            make.bottom.equalToSuperview().offset(-s(15))
        }
    }

    // MARK: - Public Methods

    /// 设置默认用户（用于初始化时显示）
    private func setupDefaultUsers() {
        // 创建默认用户数据
        var defaultUserModels: [LNUserModel] = []
        for userItem in defaultUsers {
            let userModel = LNUserModel()
            userModel.nickName = userItem.name
            userModel.country = userItem.country
            userModel.headFileName = userItem.avatar
            defaultUserModels.append(userModel)
        }
        updateUsers(defaultUserModels)
    }

    /// 更新用户数据
    func updateUsers(_ newUsers: [LNUserModel]) {
        self.users = newUsers

        DispatchQueue.main.async {
            self.collectionView.reloadData()
        }
    }
}

// MARK: - UICollectionViewDataSource
extension LNMaybeLikeView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return users.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNMaybeLikeUserCell", for: indexPath) as! LNMaybeLikeUserCell
        let userModel = users[indexPath.item]
        cell.configure(with: userModel)
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNMaybeLikeView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = s(120)
        let height = s(160)
        return CGSize(width: width, height: height)
    }
}

// MARK: - UICollectionViewDelegate
extension LNMaybeLikeView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let user = users[indexPath.item]
        // TODO: 实现用户详情功能
    }
}

// MARK: - UserItem Model
struct UserItem {
    let name: String
    let country: String
    let avatar: String
}

// MARK: - LNMaybeLikeUserCell
class LNMaybeLikeUserCell: UICollectionViewCell {
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(12)
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 4
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = s(30)
        imageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(14)
        label.textColor = .black
        label.textAlignment = .center
        return label
    }()
    
    private lazy var countryLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(12)
        label.textColor = .gray
        label.textAlignment = .center
        return label
    }()
    
    private lazy var videoCallButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "video.fill"), for: .normal)
        button.tintColor = UIColor.hex(hexString: "#04E798")
        button.backgroundColor = UIColor.hex(hexString: "#04E798").withAlphaComponent(0.1)
        button.layer.cornerRadius = s(15)
        button.addTarget(self, action: #selector(videoCallButtonTapped), for: .touchUpInside)
        return button
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(countryLabel)
        containerView.addSubview(videoCallButton)
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(15))
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(60))
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(s(8))
            make.left.right.equalToSuperview().inset(s(8))
        }
        
        countryLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(s(4))
            make.left.right.equalToSuperview().inset(s(8))
        }
        
        videoCallButton.snp.makeConstraints { make in
            make.top.equalTo(countryLabel.snp.bottom).offset(s(8))
            make.centerX.equalToSuperview()
            make.width.height.equalTo(s(30))
            make.bottom.equalToSuperview().offset(-s(15))
        }
    }
    
    func configure(with userModel: LNUserModel) {
        nameLabel.text = userModel.nickName.isEmpty ? "NAME" : userModel.nickName
        countryLabel.text = "📍 \(userModel.country.isEmpty ? "Unknown" : userModel.country)"

        // 使用LNImageLoader加载头像
        if !userModel.headFileName.isEmpty {
            LNImageLoader.loadAvatar(
                avatarImageView,
                url: userModel.headFileName,
                placeholder: UIImage(named: "default_avatar")
            )
        } else {
            avatarImageView.image = UIImage(named: "default_avatar")
        }
    }
    
    @objc private func videoCallButtonTapped() {
        print("Video call button tapped")
        // TODO: 实现视频通话功能
    }
}
