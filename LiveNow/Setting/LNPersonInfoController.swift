//
//  LNPersonInfoController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit
import HandyJSON

/// 个人信息页面控制器
/// 功能：
/// - 顶部背景图片下拉放大效果
/// - 用户信息展示（头像、昵称、性别、ID、关注按钮）
/// - 位置和关注数信息
/// - Personal Profile 标签区域
/// - Personal photo 横向滑动区域
/// - Received a gift 横向滑动区域
/// - Maybe like 横向滑动区域
/// - 底部固定按钮（Hello 和 Call Me）
class LNPersonInfoController: LNBaseController {


    var personId: Int = 0
    // MARK: - Properties
    private let headerImageHeight: CGFloat = s(350)
    private let minHeaderHeight: CGFloat = knavH

    // MARK: - Data Properties
    /// 用户信息（对应Flutter中的userBean）
    private var userBean: LNUserModel = LNUserModel()
    /// 礼物列表（对应Flutter中的giftList）
    private var giftList: [LNGiftModel] = []
    /// 图片列表（对应Flutter中的imageList）
    private var imageList: [LNAnchorFileModel] = []
    /// 视频列表（对应Flutter中的videoList）
    private var videoList: [LNAnchorFileModel] = []
    /// 标签列表（对应Flutter中的labelList）
    private var labelList: [LNAnchorLabelModel] = []
    /// 推荐用户列表（Maybe like区域显示的用户）
    private var recommendedUsers: [LNUserModel] = []
    /// 是否已关注（对应Flutter中的followed）
    private var isFollowed: Bool = false

    /// 文件类型常量
    private let fileTypeImage = "1"
    private let fileTypeVideo = "2"

    // MARK: - UI Elements

    // 主滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.delegate = self
        scrollView.contentInsetAdjustmentBehavior = .never
        return scrollView
    }()

    // 内容容器
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 顶部背景图片
    private lazy var headerImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.zPosition = -1
        imageView.image = UIImage(named: "default_avatar") // 使用默认头像作为背景
        return imageView
    }()

    // 用户信息容器
    private lazy var userInfoContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 改为透明，让背景图片和渐变色显示
        view.layer.cornerRadius = s(20)
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        return view
    }()

    // 用户头像
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = s(35)
        imageView.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        imageView.image = UIImage(named: "default_avatar")
        return imageView
    }()

    // 昵称标签
    private lazy var nicknameLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(20)
        label.textColor = .black
        label.text = "NIKENAME"
        return label
    }()

    // 性别年龄标签
    private lazy var genderAgeView: LNSexAndAgeView = {
        let view = LNSexAndAgeView()
        view.setUIWithSex(2, 23) // 默认女性，23岁
        return view
    }()

    // 用户ID标签
    private lazy var userIdLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(14)
        label.textColor = UIColor.gray
        label.text = "ID:1123334"
        return label
    }()

    // Follow按钮
    private lazy var followButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("+ Follow", for: .normal)
        button.titleLabel?.font = LNFont.medium(14)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor.hex(hexString: "#04E798")
        button.layer.cornerRadius = s(15)
        button.addTarget(self, action: #selector(followButtonTapped), for: .touchUpInside)
        return button
    }()

    // 位置信息标签
    private lazy var locationLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(14)
        label.textColor = UIColor.gray
        label.text = "📍 Indonesia"
        return label
    }()

    // 关注数标签
    private lazy var followersLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(14)
        label.textColor = UIColor.gray
        label.text = "👥 9999"
        return label
    }()

    // Personal Profile 标签视图
    private lazy var personalTagView: LNPersonalTagView = {
        let view = LNPersonalTagView()
        return view
    }()

    // Personal photo 视图
    private lazy var personalPhotoView: LNPersonalPhotoView = {
        let view = LNPersonalPhotoView()
        return view
    }()

    // Received a gift 视图
    private lazy var receivedGiftView: LNReceivedAGiftView = {
        let view = LNReceivedAGiftView()
        return view
    }()

    // Maybe like 视图
    private lazy var maybeLikeView: LNMaybeLikeView = {
        let view = LNMaybeLikeView()
        return view
    }()

    // 内容区域背景容器（包含背景图片和渐变色）
    private lazy var contentBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.layer.cornerRadius = s(20)
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.clipsToBounds = true
        return view
    }()

    // 渐变色背景视图
    private lazy var gradientBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 背景图片
    private lazy var backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.image = UIImage(named: "ic_person_bg")
        return imageView
    }()

    // 渐变色背景
    private lazy var gradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor.hex(hexString: "#D7FFF6").cgColor,
            UIColor.hex(hexString: "#FFFFFF").cgColor
        ]
        layer.startPoint = CGPoint(x: 0.5, y: 0)
        layer.endPoint = CGPoint(x: 0.5, y: 1)
        return layer
    }()

    // 底部按钮容器（固定在底部）
    private lazy var bottomButtonsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        // 移除阴影效果，让容器完全透明悬浮
        return view
    }()

    // Hello 按钮
    private lazy var helloButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle(" Hello", for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.setTitleColor(UIColor.hex(hexString: "#04E798"), for: .normal)
        button.backgroundColor = UIColor.hex(hexString: "#C3FFEE")
        button.layer.cornerRadius = s(25)

        // 设置 ic_hello 图标
        if let helloIcon = UIImage(named: "ic_hello") {
            button.setImage(helloIcon, for: .normal)
            button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: s(8))
            button.titleEdgeInsets = UIEdgeInsets(top: 0, left: s(8), bottom: 0, right: 0)
        }

        button.addTarget(self, action: #selector(helloButtonTapped), for: .touchUpInside)
        return button
    }()

    // Call Me 按钮
    private lazy var callMeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle(" Call Me", for: .normal)
        button.titleLabel?.font = LNFont.medium(16)
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = s(25)
        button.layer.masksToBounds = true

        // 设置 ic_callme 图标
        if let callmeIcon = UIImage(named: "ic_callme") {
            button.setImage(callmeIcon, for: .normal)
            button.imageView?.tintColor = .white
            button.imageView?.layer.zPosition = 1 // 确保图标在渐变层之上
            button.titleLabel?.layer.zPosition = 1 // 确保文字在渐变层之上
            button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: s(8))
            button.titleEdgeInsets = UIEdgeInsets(top: 0, left: s(8), bottom: 0, right: 0)
        }

        button.addTarget(self, action: #selector(callMeButtonTapped), for: .touchUpInside)
        return button
    }()

    // Call Me 按钮的渐变层
    private lazy var callMeGradientLayer: CAGradientLayer = {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.hex(hexString: "#04E798").cgColor,
            UIColor.hex(hexString: "#0ADCE1").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.5)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.5)
        gradientLayer.cornerRadius = s(25)
        gradientLayer.zPosition = -1 // 确保渐变层在背景
        return gradientLayer
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        // 保持全屏布局以实现下拉放大效果
        edgesForExtendedLayout = .all

        setupUI()
        setupConstraints()
        setupNavigationBarButtons()
        setupTransparentNavigationBar()

        // 如果有用户ID，加载数据
        if personId > 0 {
            loadPersonInfo()
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 确保导航栏透明
        setupTransparentNavigationBar()
    }

    override var prefersNavigationBarHide: Bool {
        return false
    }

    override var navigationSolidColor: UIColor {
        return .clear
    }

    override var navigationTitleColor: UIColor {
        return .white
    }

    override var navigationShadowHidden: Bool {
        return true
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 更新渐变层的 frame
        updateGradientLayerFrame()
    }

    // MARK: - UI Setup
    private func setupUI() {

        // 添加主要视图
        view.addSubview(scrollView)
        view.addSubview(bottomButtonsContainer)

        scrollView.addSubview(contentView)
        scrollView.addSubview(headerImageView)

        // 添加背景容器和背景元素（层级：渐变色在底层，背景图片在中间层）
        contentView.addSubview(contentBackgroundView)
        contentBackgroundView.addSubview(gradientBackgroundView)
        gradientBackgroundView.layer.addSublayer(gradientLayer)
        contentBackgroundView.addSubview(backgroundImageView)

        // 添加内容视图
        contentView.addSubview(userInfoContainerView)

        // 用户信息区域
        userInfoContainerView.addSubview(avatarImageView)
        userInfoContainerView.addSubview(nicknameLabel)
        userInfoContainerView.addSubview(genderAgeView)
        userInfoContainerView.addSubview(userIdLabel)
        userInfoContainerView.addSubview(followButton)
        userInfoContainerView.addSubview(locationLabel)
        userInfoContainerView.addSubview(followersLabel)

        // 内容区域
        contentView.addSubview(personalTagView)
        contentView.addSubview(personalPhotoView)
        contentView.addSubview(receivedGiftView)
        contentView.addSubview(maybeLikeView)

        // 底部按钮
        bottomButtonsContainer.addSubview(helloButton)
        bottomButtonsContainer.addSubview(callMeButton)

        // 添加 Call Me 按钮的渐变层
        callMeButton.layer.insertSublayer(callMeGradientLayer, at: 0)
    }

    /// 设置导航栏右侧按钮
    private func setupNavigationBarButtons() {
        // WhatsApp按钮
        let whatsappButton = UIButton(type: .custom)
        whatsappButton.setImage(UIImage(named: "ic_whatsapp"), for: .normal)
        whatsappButton.frame = CGRect(x: 0, y: 0, width: s(24), height: s(24))
        whatsappButton.addTarget(self, action: #selector(whatsappButtonTapped), for: .touchUpInside)

        // More按钮
        let moreButton = UIButton(type: .custom)
        moreButton.setImage(UIImage(named: "ic_more"), for: .normal)
        moreButton.tintColor = .white
        moreButton.frame = CGRect(x: 0, y: 0, width: s(24), height: s(24))
        moreButton.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)

        // 创建导航栏按钮项
        let whatsappBarButton = UIBarButtonItem(customView: whatsappButton)
        let moreBarButton = UIBarButtonItem(customView: moreButton)

        // 添加间距
        let spacer = UIBarButtonItem(barButtonSystemItem: .fixedSpace, target: nil, action: nil)
        spacer.width = s(16)

        // 设置右侧按钮
        navigationItem.rightBarButtonItems = [moreBarButton, spacer, whatsappBarButton]
    }

    /// 设置透明导航栏
    private func setupTransparentNavigationBar() {
        guard let navigationController = navigationController else { return }

        // 设置导航栏完全透明
        let appearance = UINavigationBarAppearance()
        appearance.configureWithTransparentBackground()
        appearance.backgroundColor = .clear
        appearance.shadowColor = .clear // 隐藏导航栏下面的分割线
        appearance.shadowImage = UIImage() // 确保分割线图片也为空

        // 设置标题样式
        appearance.titleTextAttributes = [
            .font: LNFont.medium(18),
            .foregroundColor: UIColor.white
        ]

        navigationController.navigationBar.standardAppearance = appearance
        navigationController.navigationBar.scrollEdgeAppearance = appearance
        navigationController.navigationBar.compactAppearance = appearance
        navigationController.navigationBar.isTranslucent = true

        // 额外确保分割线隐藏（兼容旧版本 iOS）
        navigationController.navigationBar.shadowImage = UIImage()
        navigationController.navigationBar.setBackgroundImage(UIImage(), for: .default)

        // 确保导航栏背景视图也是透明的
        if let lnNav = navigationController as? LNNavigationViewController {
            lnNav.applyBackground(from: self)
        }
    }

    private func setupConstraints() {
        // 头部背景图片
        headerImageView.snp.makeConstraints { make in
            make.top.left.right.equalTo(contentView)
            make.height.equalTo(headerImageHeight)
        }

        // 主滚动视图
        scrollView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalToSuperview() // 让 ScrollView 延伸到屏幕底部
        }

        // 内容视图
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
            // 确保内容高度足够包含所有子视图
            make.bottom.equalTo(maybeLikeView.snp.bottom).offset(s(120))
        }

        // 内容背景容器（从 userInfoContainerView 顶部到 maybeLikeView 底部）
        contentBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(headerImageHeight - s(30))
            make.left.right.equalToSuperview()
            make.bottom.equalTo(maybeLikeView.snp.bottom)
        }

        // 渐变色背景视图（底层）
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 背景图片（中间层）
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 用户信息容器
        userInfoContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(headerImageHeight - s(30))
            make.left.right.equalToSuperview()
        }

        // 用户头像
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(-s(15))
            make.left.equalToSuperview().offset(s(20))
            make.width.height.equalTo(s(70))
        }

        // 昵称
        nicknameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(5))
            make.left.equalTo(avatarImageView.snp.right).offset(s(15))
        }

        // 性别年龄标签
        genderAgeView.snp.makeConstraints { make in
            make.centerY.equalTo(nicknameLabel)
            make.left.equalTo(nicknameLabel.snp.right).offset(s(10))
            make.height.equalTo(s(20))
        }

        // Follow 按钮
        followButton.snp.makeConstraints { make in
            make.centerY.equalTo(nicknameLabel.snp.bottom)
            make.right.equalToSuperview().offset(-s(20))
            make.width.equalTo(s(80))
            make.height.equalTo(s(30))
        }

        // 用户ID
        userIdLabel.snp.makeConstraints { make in
            make.top.equalTo(nicknameLabel.snp.bottom).offset(s(5))
            make.left.equalTo(avatarImageView.snp.right).offset(s(15))
        }

        // 位置信息
        locationLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(s(10))
            make.left.equalTo(avatarImageView.snp.left)
        }

        // 关注数
        followersLabel.snp.makeConstraints { make in
            make.centerY.equalTo(locationLabel)
            make.left.equalTo(locationLabel.snp.right).offset(s(20))
            make.bottom.equalToSuperview().offset(-s(20))
        }

        // Personal Profile 标签视图
        personalTagView.snp.makeConstraints { make in
            make.top.equalTo(userInfoContainerView.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview().inset(s(15))
        }

        // Personal photo 视图
        personalPhotoView.snp.makeConstraints { make in
            make.top.equalTo(personalTagView.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview()
        }

        // Received a gift 视图
        receivedGiftView.snp.makeConstraints { make in
            make.top.equalTo(personalPhotoView.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview()
        }

        // Maybe like 视图
        maybeLikeView.snp.makeConstraints { make in
            make.top.equalTo(receivedGiftView.snp.bottom).offset(s(20))
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-s(120)) // 增加底部间距，为悬浮按钮留出空间
        }

        // 底部按钮容器
        bottomButtonsContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
            make.height.equalTo(s(80))
        }

        // Hello 按钮 (宽度为总宽度的 1/3)
        helloButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(15))
            make.left.equalToSuperview().offset(s(20))
            make.height.equalTo(s(50))
            make.width.equalTo((kscreenW - s(60)) / 3)
        }

        // Call Me 按钮 (宽度为总宽度的 2/3)
        callMeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(15))
            make.left.equalTo(helloButton.snp.right).offset(s(20))
            make.right.equalToSuperview().offset(-s(20))
            make.height.equalTo(s(50))
        }
    }

    /// 更新渐变层的 frame
    private func updateGradientLayerFrame() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.gradientLayer.frame = self.gradientBackgroundView.bounds

            // 更新 Call Me 按钮的渐变层
            self.callMeGradientLayer.frame = self.callMeButton.bounds
        }
    }

    /// 动态更新内容区域的约束布局
    private func updateContentConstraints() {
        // 获取当前显示的视图列表（按顺序）
        let contentViews: [(view: UIView, spacing: CGFloat)] = [
            (personalTagView, s(0)),
            (personalPhotoView, s(0)),
            (receivedGiftView, s(0)),
            (maybeLikeView, s(0))
        ]

        var previousView: UIView = userInfoContainerView
        var isFirstVisibleView = true

        for (view, spacing) in contentViews {
            if !view.isHidden {
                view.snp.remakeConstraints { make in
                    if isFirstVisibleView {
                        make.top.equalTo(previousView.snp.bottom).offset(spacing)
                        isFirstVisibleView = false
                    } else {
                        make.top.equalTo(previousView.snp.bottom).offset(spacing)
                    }

                    // 设置左右约束
                    if view == personalTagView {
                        make.left.right.equalToSuperview().inset(s(15))
                    } else {
                        make.left.right.equalToSuperview()
                    }
                }
                previousView = view
            } else {
                // 隐藏的视图设置高度为0
                view.snp.remakeConstraints { make in
                    make.top.equalTo(previousView.snp.bottom)
                    if view == personalTagView {
                        make.left.right.equalToSuperview().inset(s(15))
                    } else {
                        make.left.right.equalToSuperview()
                    }
                    make.height.equalTo(0)
                }
            }
        }

        // 更新最后一个视图的底部约束
        maybeLikeView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-s(120))
        }

        // 使用动画平滑更新布局
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseInOut], animations: {
            self.view.layoutIfNeeded()
        }, completion: nil)
    }



    // MARK: - Event Handlers
    @objc private func followButtonTapped() {
        print("Follow button tapped")
        followUnfollow()
    }

    /// 关注/取消关注切换（对应Flutter中的followUnfollow方法）
    private func followUnfollow() {
        guard userBean.id > 0 else { return }

        if isFollowed {
            // 取消关注
            isFollowed = false
            updateFollowButton()
            unFollow(anchorId: "\(userBean.id)")
        } else {
            // 关注用户
            isFollowed = true
            updateFollowButton()
            follow(anchorId: "\(userBean.id)")
        }
    }

    /// 关注用户（对应Flutter中的follow方法）
    private func follow(anchorId: String) {
        let params = ["id": anchorId, "role": "1"]

        NetWorkRequest(LNApiProfile.followUser(par: params), completion: { result in
            print("关注成功")
        }, failure: { error in
            DispatchQueue.main.async { [weak self] in
                // 关注失败，恢复状态
                self?.isFollowed = false
                self?.updateFollowButton()
                print("关注失败: \(error.localizedDescription)")
            }
        })
    }

    /// 取消关注用户（对应Flutter中的unFollow方法）
    private func unFollow(anchorId: String) {
        let params = ["id": anchorId]

        NetWorkRequest(LNApiProfile.unfollowUser(par: params), completion: { result in
            print("取消关注成功")
        }, failure: { error in
            DispatchQueue.main.async { [weak self] in
                // 取消关注失败，恢复状态
                self?.isFollowed = true
                self?.updateFollowButton()
                print("取消关注失败: \(error.localizedDescription)")
            }
        })
    }

    @objc private func helloButtonTapped() {
        print("Hello button tapped")
        onChat()
    }

    /// 显示获取WhatsApp弹框
    private func showWhatsAppModal() {
        let modal = LNWhatsAppModal()
        // 获取应用主窗口，确保弹框覆盖整个屏幕包括导航栏
        if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            modal.show(in: window)
        } else if let window = UIApplication.shared.windows.first {
            modal.show(in: window)
        } else {
            // 降级方案：使用导航控制器的视图
            modal.show(in: navigationController?.view ?? view)
        }
    }

    @objc private func callMeButtonTapped() {
        print("Call Me button tapped")
        onVideo()
    }

    @objc private func whatsappButtonTapped() {
        print("WhatsApp button tapped")
        // 显示获取WhatsApp弹框
        showWhatsAppModal()
    }

    @objc private func moreButtonTapped() {
        print("More button tapped")
        // 显示更多功能菜单弹框
        showMoreActionModal()
    }

    /// 显示更多功能菜单弹框
    private func showMoreActionModal() {
        let modal = LNMoreActionModal()

        // 设置回调
        modal.onDelete = { [weak self] in
            self?.handleDeleteAction()
        }

        modal.onReport = { [weak self] in
            self?.handleReportAction()
        }

        // 获取应用主窗口，确保弹框覆盖整个屏幕包括导航栏
        if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) {
            modal.show(in: window)
        } else if let window = UIApplication.shared.windows.first {
            modal.show(in: window)
        } else {
            // 降级方案：使用导航控制器的视图
            modal.show(in: navigationController?.view ?? view)
        }
    }

    /// 处理删除操作
    private func handleDeleteAction() {
        print("Delete action triggered")
        // TODO: 实现删除功能
    }

    /// 处理举报操作
    private func handleReportAction() {
        print("Report action triggered")
        // TODO: 实现举报功能
    }

    // MARK: - Network Methods

    /// 加载个人信息数据
    private func loadPersonInfo() {
        guard personId > 0 else {
            print("用户ID无效")
            return
        }

        let anchorId = "\(personId)"

        // 并发调用三个接口
        getPersonInfo(anchorId: anchorId)
        getFollowFlag(anchorId: anchorId)
        getUserGift(anchorId: anchorId)
    }

    /// 获取主播详情信息
    /// - Parameter anchorId: 主播ID
    private func getPersonInfo(anchorId: String) {
        let params = ["id": anchorId]

        NetWorkRequest(LNApiAnchor.anchorDetail(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            // 解析用户信息
            if let data = result["data"] as? [String: Any],
               let userBean = LNUserModel.deserialize(from: data) {

                DispatchQueue.main.async {
                    self.userBean = userBean
                    self.updateUserInfo(userBean)

                    // 处理文件列表
                    if !userBean.anchorFileList.isEmpty {
                        var tmpImageList: [LNAnchorFileModel] = []
                        var tmpVideoList: [LNAnchorFileModel] = []

                        for file in userBean.anchorFileList {
                            if file.fileType == self.fileTypeImage {
                                tmpImageList.append(file)
                            } else if file.fileType == self.fileTypeVideo {
                                tmpVideoList.append(file)
                            }
                        }

                        self.imageList = tmpImageList
                        self.videoList = tmpVideoList
                        self.updatePhotoSection()
                    }

                    // 处理标签列表
                    self.labelList = userBean.anchorLabelList
                    self.updateTagSection()
                }
            }

        }, failure: { error in
            DispatchQueue.main.async {
                print("获取个人信息失败: \(error.localizedDescription)")
                // TODO: 显示错误提示
            }
        })
    }

    /// 获取关注状态
    /// - Parameter anchorId: 主播ID
    private func getFollowFlag(anchorId: String) {
        let params = ["id": anchorId]

        NetWorkRequest(LNApiProfile.followFlag(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            // 解析关注状态
            if let data = result["data"] as? String {
                DispatchQueue.main.async {
                    self.isFollowed = (data == "1")
                    self.updateFollowButton()
                }
            }

        }, failure: { error in
            DispatchQueue.main.async {
                print("获取关注状态失败: \(error.localizedDescription)")
                // TODO: 显示错误提示
            }
        })
    }

    /// 获取用户礼物列表
    /// - Parameter anchorId: 主播ID
    private func getUserGift(anchorId: String) {
        let params = ["anchorId": anchorId]

        NetWorkRequest(LNApiProfile.userGiftList(par: params), completion: { [weak self] result in
            guard let self = self else { return }

            // 解析礼物列表
            if let data = result["data"] as? [[String: Any]] {
                let giftList = data.compactMap { LNGiftModel.deserialize(from: $0) }

                DispatchQueue.main.async {
                    self.giftList = giftList
                    self.updateGiftSection()
                }
            }

        }, failure: { error in
            DispatchQueue.main.async {
                print("获取礼物列表失败: \(error.localizedDescription)")
                // TODO: 显示错误提示
            }
        })
    }

    // MARK: - UI Update Methods

    /// 更新用户信息显示
    /// - Parameter userBean: 用户数据
    private func updateUserInfo(_ userBean: LNUserModel) {
        // 更新头像
        if !userBean.headFileName.isEmpty {
            // 使用LNImageLoader加载头像
            LNImageLoader.loadAvatar(
                avatarImageView,
                url: userBean.headFileName,
                placeholder: UIImage(named: "default_avatar")
            )

            // 同时更新背景图片
            LNImageLoader.loadCover(
                headerImageView,
                url: userBean.headFileName,
                placeholder: UIImage(named: "default_avatar")
            )
        }

        // 更新昵称
        nicknameLabel.text = userBean.nickName.isEmpty ? "NICKNAME" : userBean.nickName

        // 更新性别年龄
        let gender = userBean.gender == "1" ? 1 : 2 // 1:男性, 2:女性
        genderAgeView.setUIWithSex(gender, userBean.age)

        // 更新用户ID
        userIdLabel.text = "ID:\(userBean.id)"

        // 更新位置信息
        locationLabel.text = "📍 \(userBean.country.isEmpty ? "Unknown" : userBean.country)"

        // 更新关注数
        followersLabel.text = "👥 \(userBean.fansNum)"
    }

    /// 更新关注按钮状态
    private func updateFollowButton() {
        if isFollowed {
            followButton.setTitle("Following", for: .normal)
            followButton.backgroundColor = UIColor.gray
        } else {
            followButton.setTitle("+ Follow", for: .normal)
            followButton.backgroundColor = UIColor.hex(hexString: "#04E798")
        }
    }

    /// 更新标签区域
    private func updateTagSection() {
        print("更新标签区域，标签数量: \(labelList.count)")

        // 更新标签视图数据
        personalTagView.updateTags(labelList)

        // 如果没有标签数据，隐藏标签区域
        let shouldHide = labelList.isEmpty
        if personalTagView.isHidden != shouldHide {
            personalTagView.isHidden = shouldHide
            // 视图显示状态改变时，更新约束布局
            updateContentConstraints()
        }
    }

    /// 更新照片区域
    private func updatePhotoSection() {
        print("更新照片区域，图片数量: \(imageList.count)")

        // 更新照片视图数据
        personalPhotoView.updatePhotos(imageList)

        // 如果没有照片数据，隐藏照片区域
        let shouldHide = imageList.isEmpty
        if personalPhotoView.isHidden != shouldHide {
            personalPhotoView.isHidden = shouldHide
            // 视图显示状态改变时，更新约束布局
            updateContentConstraints()
        }
    }

    /// 更新礼物区域
    private func updateGiftSection() {
        print("更新礼物区域，礼物数量: \(giftList.count)")

        // 更新礼物视图数据
        receivedGiftView.updateGifts(giftList)

        // 如果没有礼物数据，隐藏礼物区域
        let shouldHide = giftList.isEmpty
        if receivedGiftView.isHidden != shouldHide {
            receivedGiftView.isHidden = shouldHide
            // 视图显示状态改变时，更新约束布局
            updateContentConstraints()
        }
    }

    /// 更新Maybe Like区域
    private func updateMaybeLikeSection() {
        print("更新Maybe Like区域，推荐用户数量: \(recommendedUsers.count)")

        // 更新Maybe Like视图数据
        maybeLikeView.updateUsers(recommendedUsers)

        // Maybe Like区域通常总是显示，即使没有数据也显示默认内容
        // 如果需要隐藏，可以取消注释下面的代码
        /*
        if recommendedUsers.isEmpty {
            maybeLikeView.isHidden = true
        } else {
            maybeLikeView.isHidden = false
        }
        */
    }

    // MARK: - Business Logic Methods

    /// 聊天功能（对应Flutter中的onChat方法）
    private func onChat() {
        // TODO: 跳转到聊天页面
        print("跳转到聊天页面，用户ID: \(userBean.id)")
        // 这里应该跳转到聊天页面，传递userBean数据
    }

    /// 视频通话功能（对应Flutter中的onVideo方法）
    private func onVideo() {
        // 获取当前用户的钻石数量
        let userDiamond = LNUserManager.shared.userModel?.diamond ?? 0

        if userDiamond < userBean.videoPrice {
            // 钻石不足，显示充值弹框
            showRechargeDialog()
        } else {
            // 钻石充足，跳转到视频通话页面
            startVideoChat()
        }
    }

    /// 显示充值弹框
    private func showRechargeDialog() {
        // TODO: 显示充值弹框
        print("钻石不足，需要充值。当前价格: \(userBean.videoPrice)")
    }

    /// 开始视频通话
    private func startVideoChat() {
        // TODO: 跳转到视频通话页面
        print("开始视频通话，用户ID: \(userBean.id)")
        // 这里应该跳转到视频通话页面，传递相关参数
    }

    /// 获取文件URL（对应Flutter中的getFileUrl方法）
    /// - Parameters:
    ///   - type: 文件类型
    ///   - index: 索引
    /// - Returns: 文件URL
    func getFileUrl(type: String, index: Int) -> String {
        if type == fileTypeImage && index < imageList.count {
            return imageList[index].fileName
        } else if type == fileTypeVideo && index < videoList.count {
            return videoList[index].thumbnail
        }
        return ""
    }
}

// MARK: - UIScrollViewDelegate
extension LNPersonInfoController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let offsetY = scrollView.contentOffset.y

        // 下拉图片放大效果
        if offsetY < 0 {
            let scale = 1 + abs(offsetY) / headerImageHeight
            headerImageView.transform = CGAffineTransform(scaleX: scale, y: scale)

            // 调整图片位置，保持顶部对齐
            let deltaY = abs(offsetY) / 2
            headerImageView.snp.updateConstraints { make in
                make.top.equalTo(contentView).offset(-deltaY)
                make.height.equalTo(headerImageHeight + abs(offsetY))
            }
        } else {
            // 恢复正常状态
            headerImageView.transform = .identity
            headerImageView.snp.updateConstraints { make in
                make.top.equalTo(contentView)
                make.height.equalTo(headerImageHeight)
            }
        }

        // 更新导航栏透明度和标题颜色
        updateNavigationBarAppearance(with: offsetY)
    }

    /// 根据滚动偏移量更新导航栏外观
    private func updateNavigationBarAppearance(with offsetY: CGFloat) {
        guard let navigationController = navigationController else { return }

        // 计算透明度：从完全透明到完全不透明的过渡
        let fadeDistance: CGFloat = headerImageHeight - minHeaderHeight
        let alpha = max(0, min(1.0, offsetY / fadeDistance))

        // 创建新的外观配置
        let appearance = UINavigationBarAppearance()
        appearance.configureWithTransparentBackground()
        appearance.backgroundColor = UIColor.white.withAlphaComponent(alpha)
        appearance.shadowColor = .clear // 始终隐藏导航栏下面的分割线
        appearance.shadowImage = UIImage() // 确保分割线图片也为空

        // 根据背景透明度调整标题颜色和按钮颜色
        let titleColor = alpha > 0.5 ? UIColor.black : UIColor.white
        appearance.titleTextAttributes = [
            .font: LNFont.medium(18),
            .foregroundColor: titleColor
        ]

        // 更新导航栏按钮颜色
        updateNavigationBarButtonColors(titleColor)

        // 应用外观配置
        navigationController.navigationBar.standardAppearance = appearance
        navigationController.navigationBar.scrollEdgeAppearance = appearance
        navigationController.navigationBar.compactAppearance = appearance

        // 额外确保分割线隐藏（兼容旧版本 iOS）
        navigationController.navigationBar.shadowImage = UIImage()
        navigationController.navigationBar.setBackgroundImage(UIImage(), for: .default)

        // 同步更新自定义导航控制器的背景
        if navigationController is LNNavigationViewController {
            // 实现动态透明度
            if alpha > 0 {
                // 使用带透明度的白色
                navigationController.view.backgroundColor = UIColor.white.withAlphaComponent(alpha)
            } else {
                // 完全透明
                navigationController.view.backgroundColor = .clear
            }
        }
    }

    /// 更新导航栏按钮颜色
    private func updateNavigationBarButtonColors(_ color: UIColor) {
        // 更新右侧按钮颜色
        if let rightBarButtonItems = navigationItem.rightBarButtonItems {
            for barButtonItem in rightBarButtonItems {
                if let customView = barButtonItem.customView as? UIButton {
                    customView.tintColor = color
                }
            }
        }
    }
}
