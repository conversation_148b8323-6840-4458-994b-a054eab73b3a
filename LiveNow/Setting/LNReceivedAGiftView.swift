//
//  LNReceivedAGiftView.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/18.
//

import UIKit
import SnapKit

/// Received a Gift 横向滑动视图
/// 展示用户收到的礼物集合，支持横向滑动浏览
class LNReceivedAGiftView: UIView {
    
    // MARK: - Properties
    private var gifts: [LNGiftModel] = []
    private let defaultGifts = [
        GiftItem(name: "<PERSON>", count: 10, emoji: "🌹"),
        GiftItem(name: "Heart", count: 10, emoji: "💖"),
        GiftItem(name: "Diamond", count: 10, emoji: "💎"),
        GiftItem(name: "Crown", count: 10, emoji: "👑"),
        GiftItem(name: "Star", count: 10, emoji: "⭐")
    ]
    
    // MARK: - UI Elements
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "🎁 Received a gift"
        label.font = LNFont.bold(16)
        label.textColor = UIColor.hex(hexString: "#04E798")
        return label
    }()
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = s(10)
        layout.minimumInteritemSpacing = 0
        layout.sectionInset = UIEdgeInsets(top: 0, left: s(15), bottom: 0, right: s(15))
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LNGiftCell.self, forCellWithReuseIdentifier: "LNGiftCell")
        return collectionView
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
        setupDefaultGifts()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        
        addSubview(titleLabel)
        addSubview(collectionView)
    }
    
    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(s(15))
            make.right.equalToSuperview().offset(-s(15))
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(s(10))
            make.left.right.equalToSuperview()
            make.height.equalTo(s(100))
            make.bottom.equalToSuperview().offset(-s(15))
        }
    }

    // MARK: - Public Methods

    /// 设置默认礼物（用于初始化时显示）
    private func setupDefaultGifts() {
        // 创建默认礼物数据
        var defaultGiftModels: [LNGiftModel] = []
        for (index, giftItem) in defaultGifts.enumerated() {
            let giftModel = LNGiftModel()
            giftModel.giftId = "\(index + 1)"
            giftModel.name = giftItem.name
            giftModel.imageUrl = giftItem.emoji // 使用emoji作为图片
            giftModel.price = giftItem.count
            defaultGiftModels.append(giftModel)
        }
        updateGifts(defaultGiftModels)
    }

    /// 更新礼物数据
    func updateGifts(_ newGifts: [LNGiftModel]) {
        self.gifts = newGifts

        DispatchQueue.main.async {
            self.collectionView.reloadData()
        }
    }
}

// MARK: - UICollectionViewDataSource
extension LNReceivedAGiftView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return gifts.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "LNGiftCell", for: indexPath) as! LNGiftCell
        let giftModel = gifts[indexPath.item]
        cell.configure(with: giftModel)
        return cell
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension LNReceivedAGiftView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let size = s(80)
        return CGSize(width: size, height: size)
    }
}

// MARK: - UICollectionViewDelegate
extension LNReceivedAGiftView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let gift = gifts[indexPath.item]
        print("Gift selected: \(gift.name)")
        // TODO: 实现礼物详情功能
    }
}

// MARK: - GiftItem Model
struct GiftItem {
    let name: String
    let count: Int
    let emoji: String
}

// MARK: - LNGiftCell
class LNGiftCell: UICollectionViewCell {
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(8)
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 4
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        return view
    }()
    
    private lazy var emojiLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: s(30))
        label.textAlignment = .center
        return label
    }()
    
    private lazy var countLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(14)
        label.textColor = .black
        label.textAlignment = .center
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(emojiLabel)
        containerView.addSubview(countLabel)
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        emojiLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(10))
            make.centerX.equalToSuperview()
        }
        
        countLabel.snp.makeConstraints { make in
            make.top.equalTo(emojiLabel.snp.bottom).offset(s(5))
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-s(10))
        }
    }
    
    func configure(with giftModel: LNGiftModel) {
        // 如果imageUrl是emoji，直接显示；否则尝试加载图片
        if giftModel.imageUrl.count <= 2 && giftModel.imageUrl.unicodeScalars.allSatisfy({ $0.properties.isEmoji }) {
            emojiLabel.text = giftModel.imageUrl
        } else {
            // 这里可以扩展为加载网络图片，暂时显示礼物名称的首字母
            emojiLabel.text = String(giftModel.name.prefix(1))
        }

        countLabel.text = "\(giftModel.price)"
    }
}
