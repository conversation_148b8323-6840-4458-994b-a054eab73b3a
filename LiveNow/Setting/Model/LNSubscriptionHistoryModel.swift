//
//  LNSubscriptionHistoryModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/31.
//

import Foundation
import HandyJSON

// MARK: - 订阅状态枚举
enum LNSubscriptionStatus {
    case finished
    case unfinished

    var displayText: String {
        switch self {
        case .finished:
            return "Finished"
        case .unfinished:
            return "Unfinished"
        }
    }

    var textColor: UIColor {
        switch self {
        case .finished:
            return UIColor.hex(hexString: "#00FF91") // 绿色
        case .unfinished:
            return UIColor.hex(hexString: "#FF1500") // 红色
        }
    }
}

// MARK: - LNRecharge订阅扩展
/// LNRecharge模型的订阅相关扩展
extension LNRecharge {

    /// VIP类型显示
    var vipType: String {
        if !itemName.isEmpty {
            return itemName
        }

        // 根据level或其他字段判断VIP类型
        switch level {
        case "1":
            return "Bronze VIP"
        case "2":
            return "Silver VIP"
        case "3":
            return "Gold VIP"
        default:
            return "VIP"
        }
    }

    /// 格式化显示价格
    var priceDisplay: String {
        let price = displayPrice
        return price.hasPrefix("US$") ? price : "US$\(price)"
    }

    /// 格式化时间显示（需要添加时间字段到LNRecharge）
    var timeDisplay: String {
        // 这里可能需要从其他字段获取时间，或者添加新字段
        return "2025-01-02 16:25:45"
    }

    /// 订阅状态
    var subscriptionStatus: LNSubscriptionStatus {
        // 根据实际的状态字段判断，这里需要根据API返回的字段调整
        // 可能需要添加status字段到LNRecharge模型
        return .finished // 默认值，需要根据实际情况调整
    }

    /// 是否已完成
    var isFinished: Bool {
        return subscriptionStatus == .finished
    }
}

// MARK: - 订阅历史请求参数
/// 订阅历史请求参数
struct LNSubscriptionHistoryRequest {
    /// 价格类型：1-充值，2-订阅，3-充值+订阅
    let priceType: String
    let page: Int
    let size: Int

    init(priceType: String = "2", page: Int = 1, size: Int = 10) {
        self.priceType = priceType
        self.page = page
        self.size = size
    }

    /// 转换为请求参数字典
    func toParameters() -> [String: Any] {
        return [
            "priceType": priceType,
            "page": page,
            "size": size
        ]
    }
}

// MARK: - 兼容性模型
/// 为了兼容现有的UI组件，保留Record结构
struct LNSubscriptionRecord {
    let vipType: String
    let priceUSD: String
    let time: String
    let status: LNSubscriptionStatus

    /// 从LNRecharge模型创建
    init(from recharge: LNRecharge) {
        self.vipType = recharge.vipType
        self.priceUSD = recharge.priceDisplay
        self.time = recharge.timeDisplay
        self.status = recharge.subscriptionStatus
    }

    /// 直接创建
    init(vipType: String, priceUSD: String, time: String, status: LNSubscriptionStatus) {
        self.vipType = vipType
        self.priceUSD = priceUSD
        self.time = time
        self.status = status
    }
}

// MARK: - 订阅历史管理器
/// 订阅历史管理器 - 处理订阅历史相关的网络请求
class LNSubscriptionHistoryManager {
    static let shared = LNSubscriptionHistoryManager()
    private init() {}

    /// 获取订阅历史列表
    /// - Parameters:
    ///   - page: 页码
    ///   - size: 每页数量
    ///   - completion: 成功回调
    ///   - failure: 失败回调
    func fetchSubscriptionHistoryList(
        page: Int = 1,
        size: Int = 10,
        completion: @escaping ([LNRecharge]) -> Void,
        failure: @escaping (Error) -> Void
    ) {
        let request = LNSubscriptionHistoryRequest(priceType: "2", page: page, size: size)

        NetWorkRequest(
            LNApiProfile.rechargePriceList(par: request.toParameters()),
            completion: { result in
                DispatchQueue.main.async {
                    // 解析返回数据
                    if let dataArray = result["data"] as? [[String: Any]] {
                        var rechargeList: [LNRecharge] = []
                        for item in dataArray {
                            if let recharge = LNRecharge.deserialize(from: item) {
                                rechargeList.append(recharge)
                            }
                        }
                        completion(rechargeList)
                    } else {
                        let error = NSError(domain: "LNSubscriptionHistoryManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
                        failure(error)
                    }
                }
            },
            failure: { error in
                DispatchQueue.main.async {
                    failure(error)
                }
            }
        )
    }
}
